import React from "react";
import { ComponentLoadingSpinner } from "../shared/component-loading-spinner";
import {
    MainButtonWithIcon,
    MainStylePropsWithIcon,
} from "./button-with-icon.style";
import { ButtonWithIconProps, ButtonWithIconRef } from "./types";

/**
 * Creates a click handler that prevents clicks when aria-disabled is true
 */
const handleClick = (
    ariaDisabled: boolean | undefined,
    onClick: React.MouseEventHandler<HTMLButtonElement> | undefined
) => {
    return (e: React.MouseEvent<HTMLButtonElement>) => {
        if (ariaDisabled) {
            e.preventDefault();
            return;
        }
        if (onClick) {
            onClick(e);
        }
    };
};

/**
 * NOTE: Due to the way we intend to customise both components, with forwardRef behaviour
 * we are unable to create a single component and have them share.
 *
 * Will refactor if there is a better way
 */
const DefaultComponent = (
    props: ButtonWithIconProps,
    ref: ButtonWithIconRef
) => {
    const {
        children,
        disabled = false,
        styleType = "default",
        danger = false,
        icon,
        iconPosition = "left",
        loading = false,
        ariaDisabled,
        onClick,
        ...otherProps
    } = props;

    const mainStyle: MainStylePropsWithIcon = {
        $buttonIconPosition: iconPosition,
        $buttonStyle: disabled ? "disabled" : styleType,
        $buttonSizeStyle: "default",
        $buttonIsDanger: danger,
    };

    return (
        <MainButtonWithIcon
            ref={ref}
            data-testid={otherProps["data-testid"] || "button-with-icon"}
            disabled={disabled}
            aria-disabled={ariaDisabled}
            onClick={handleClick(ariaDisabled, onClick)}
            {...mainStyle}
            {...otherProps}
        >
            {loading ? <ComponentLoadingSpinner /> : icon}
            <span>{children}</span>
        </MainButtonWithIcon>
    );
};
DefaultComponent.displayName = "ButtonWithIcon.Default";

const SmallComponent = (props: ButtonWithIconProps, ref: ButtonWithIconRef) => {
    const {
        children,
        disabled = false,
        styleType = "default",
        danger = false,
        icon,
        iconPosition = "left",
        loading = false,
        ariaDisabled,
        onClick,
        ...otherProps
    } = props;

    const mainStyle: MainStylePropsWithIcon = {
        $buttonIconPosition: iconPosition,
        $buttonStyle: disabled ? "disabled" : styleType,
        $buttonSizeStyle: "small",
        $buttonIsDanger: danger,
    };

    return (
        <MainButtonWithIcon
            ref={ref}
            data-testid={otherProps["data-testid"] || "button-with-icon"}
            disabled={disabled}
            aria-disabled={ariaDisabled}
            onClick={handleClick(ariaDisabled, onClick)}
            {...mainStyle}
            {...otherProps}
        >
            {loading ? <ComponentLoadingSpinner /> : icon}
            <span>{children}</span>
        </MainButtonWithIcon>
    );
};
SmallComponent.displayName = "ButtonWithIcon.Small";

export const ButtonWithIcon = {
    Default: React.forwardRef(DefaultComponent),
    Small: React.forwardRef(SmallComponent),
};
