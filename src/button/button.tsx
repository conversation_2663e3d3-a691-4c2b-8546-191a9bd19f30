import React from "react";
import { Main, MainStyleProps, Spinner } from "./button.style";
import { ButtonProps, ButtonRef } from "./types";

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================
const getDisabledState = (disabled: boolean, focusableWhenDisabled: boolean) => {
    return {
        shouldBeUnfocusable: disabled && !focusableWhenDisabled,
        shouldPreventInteraction: disabled && focusableWhenDisabled,
    };
};

const createClickHandler = (
    shouldPreventInteraction: boolean,
    onClick?: React.MouseEventHandler<HTMLButtonElement>
) => {
    return (e: React.MouseEvent<HTMLButtonElement>) => {
        if (shouldPreventInteraction) {
            e.preventDefault();
            e.stopPropagation();
            return;
        }
        onClick?.(e);
    };
};

const createKeyDownHandler = (
    shouldPreventInteraction: boolean,
    onKeyDown?: React.KeyboardEventHandler<HTMLButtonElement>
) => {
    return (e: React.KeyboardEvent<HTMLButtonElement>) => {
        if (shouldPreventInteraction && (e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            e.stopPropagation();
            return;
        }
        onKeyDown?.(e);
    };
};

/**
 * NOTE: Due to the way we intend to customise both components, with forwardRef behaviour
 * we are unable to create a single component and have them share.
 *
 * Will refactor if there is a better way
 */
const DefaultComponent = (props: ButtonProps, ref: ButtonRef) => {
    const {
        children,
        disabled = false,
        loading = false,
        styleType = "default",
        danger = false,
        focusableWhenDisabled = false,
        onClick,
        onKeyDown,
        ...otherProps
    } = props;

    // When disabled=true and focusableWhenDisabled=true, keep focusable but prevent all interactions
    const { shouldBeUnfocusable, shouldPreventInteraction } = getDisabledState(disabled, focusableWhenDisabled);

    const mainStyle: MainStyleProps = {
        $buttonStyle: disabled ? "disabled" : styleType,
        $buttonSizeStyle: "default",
        $buttonIsDanger: danger,
    };

    return (
        <Main
            ref={ref}
            data-testid={otherProps["data-testid"] || "button"}
            disabled={shouldBeUnfocusable}
            onClick={createClickHandler(shouldPreventInteraction, onClick)}
            onKeyDown={createKeyDownHandler(shouldPreventInteraction, onKeyDown)}
            {...mainStyle}
            {...otherProps}
        >
            {loading && <Spinner />}
            <span>{children}</span>
        </Main>
    );
};
DefaultComponent.displayName = "Button.Default";

const SmallComponent = (props: ButtonProps, ref: ButtonRef) => {
    const {
        children,
        disabled = false,
        loading = false,
        styleType = "default",
        danger = false,
        focusableWhenDisabled = false,
        onClick,
        onKeyDown,
        ...otherProps
    } = props;

    const { shouldBeUnfocusable, shouldPreventInteraction } = getDisabledState(disabled, focusableWhenDisabled);

    const mainStyle: MainStyleProps = {
        $buttonStyle: disabled ? "disabled" : styleType,
        $buttonSizeStyle: "small",
        $buttonIsDanger: danger,
    };

    return (
        <Main
            ref={ref}
            data-testid={otherProps["data-testid"] || "button"}
            disabled={shouldBeUnfocusable}
            onClick={createClickHandler(shouldPreventInteraction, onClick)}
            onKeyDown={createKeyDownHandler(shouldPreventInteraction, onKeyDown)}
            {...mainStyle}
            {...otherProps}
        >
            {loading && <Spinner />}
            <span>{children}</span>
        </Main>
    );
};
SmallComponent.displayName = "Button.Small";

const LargeComponent = (props: ButtonProps, ref: ButtonRef) => {
    const {
        children,
        disabled = false,
        loading = false,
        styleType = "default",
        danger = false,
        focusableWhenDisabled = false,
        onClick,
        onKeyDown,
        ...otherProps
    } = props;

    const shouldBeUnfocusable = disabled && !focusableWhenDisabled;
    const shouldPreventInteraction = disabled && focusableWhenDisabled;

    const mainStyle: MainStyleProps = {
        $buttonStyle: disabled ? "disabled" : styleType,
        $buttonSizeStyle: "large",
        $buttonIsDanger: danger,
    };

    return (
        <Main
            ref={ref}
            data-testid={otherProps["data-testid"] || "button"}
            disabled={shouldBeUnfocusable}
            onClick={createClickHandler(shouldPreventInteraction, onClick)}
            onKeyDown={createKeyDownHandler(shouldPreventInteraction, onKeyDown)}
            {...mainStyle}
            {...otherProps}
        >
            {loading && <Spinner />}
            <span>{children}</span>
        </Main>
    );
};
LargeComponent.displayName = "Button.Large";

export const Button = {
    Default: React.forwardRef(DefaultComponent),
    Small: React.forwardRef(SmallComponent),
    Large: React.forwardRef(LargeComponent),
};
