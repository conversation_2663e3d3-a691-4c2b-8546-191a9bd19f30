import React from "react";

/**
 * Creates an event handler that prevents interactions when aria-disabled is true.
 * Prevents both click and keyboard (Enter/Space) interactions.
 */
export const createInteractionHandler = <E extends React.SyntheticEvent<HTMLElement>>(
  ariaDisabled: boolean | undefined,
  userHandler?: (e: E) => void
) => {
  return (e: E) => {
    if (ariaDisabled) {
      if (
        e.type === 'click' ||
        (e.type === 'keydown' &&
          ('key' in e && (e.key === 'Enter' || e.key === ' ')))
      ) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }
    }
    userHandler?.(e);
  };
};
