import React from "react";

export namespace ButtonHelper {
    /**
     * Creates an event handler that prevents interactions when shouldPrevent is true.
     * Prevents both click and keyboard (Enter/Space) interactions.
     */
    export const handleInteraction = <T extends React.SyntheticEvent>(
        shouldPrevent: boolean,
        e: T,
        userHandler?: (e: T) => void
    ) => {
        if (shouldPrevent) {
            if (
                e.type === 'click' ||
                (e.type === 'keydown' && 'key' in e && (e.key === 'Enter' || e.key === ' '))
            ) {
                e.preventDefault();
                e.stopPropagation();
                return;
            }
        }
        userHandler?.(e);
    };
}
